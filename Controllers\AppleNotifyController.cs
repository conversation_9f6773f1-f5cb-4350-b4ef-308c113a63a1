using System;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Threading.Tasks;
using System.Web.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using AppleNotificationApi.Helpers;

namespace AppleNotificationApi.Controllers
{
    public class AppleNotifyController : ApiController
    {
        [HttpPost]
        [AllowAnonymous]
        [Route("AppleNotify")]
        public async Task<IHttpActionResult> AppleNotify([FromBody] JObject body)
        {
            try
            {
                var signedPayload = body["signedPayload"]?.ToString();
                if (string.IsNullOrEmpty(signedPayload))
                    return BadRequest("signedPayload is required.");

                var payload = AppleJwsDecoder.DecodeAndValidate(signedPayload);
                var signedTransactionInfo = payload["data"]?["signedTransactionInfo"]?.ToString();
                var transactionInfo = AppleJwsDecoder.DecodeAndValidate(signedTransactionInfo);

                JObject renewalInfo = null;
                var signedRenewalInfo = payload["data"]?["signedRenewalInfo"]?.ToString();
                if (!string.IsNullOrEmpty(signedRenewalInfo))
                {
                    renewalInfo = AppleJwsDecoder.DecodeAndValidate(signedRenewalInfo);
                }

                var logObject = new JObject
                {
                    ["rootPayload"] = payload,
                    ["transactionInfo"] = transactionInfo,
                    ["renewalInfo"] = renewalInfo
                };

                await LogToDatabase(logObject.ToString(Formatting.Indented));
                return Ok();
            }
            catch (Exception ex)
            {
                await LogToDatabase("AppleNotify Exception: " + ex.ToString());
                return InternalServerError(ex);
            }
        }

        private async Task LogToDatabase(string json)
        {
            using (SqlConnection conn = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["DBDiginotice"].ConnectionString))
            using (SqlCommand cmd = new SqlCommand("Usp_dump_applenotification", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@json", json);
                await conn.OpenAsync();
                await cmd.ExecuteNonQueryAsync();
            }
        }
    }
}
